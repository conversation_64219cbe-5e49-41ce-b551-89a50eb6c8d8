﻿using System;
using System.Collections.Generic;

namespace HeroLogin.Database.Entities.Account;

public partial class TblAccount
{
    public int Id { get; set; }

    public string FldId { get; set; } = null!;

    public string FldPassword { get; set; } = null!;

    public string? Pass2 { get; set; }

    public int? FldOnline { get; set; }

    public string? FldCard { get; set; }

    public string? FldName { get; set; }

    public string? FldQu { get; set; }

    public string? FldAnswer { get; set; }

    public string? FldRegip { get; set; }

    public DateTime? FldRegtime { get; set; }

    public DateTime? FldLastlogintime { get; set; }

    public string? FldLastloginip { get; set; }

    public int? FldVip { get; set; }

    public int? FldQsonoff { get; set; }

    public int? FldZt { get; set; }

    public int? FldSex { get; set; }

    public int? FldRxpiont { get; set; }

    public int FldRxpiontx { get; set; }

    public DateTime? FldViptim { get; set; }

    public string? FldMail { get; set; }

    public string? FldFq { get; set; }

    public int FldDenbu { get; set; }

    public int FldQuayso { get; set; }

    public string? FldGhiChu { get; set; }

    public int? FldDiemThuong { get; set; }

    public int FldSvip { get; set; }

    public int? FldGiftcode { get; set; }

    public int? FldThelucchien { get; set; }

    public int? FldThelucchien2 { get; set; }

    public int? FldJf { get; set; }

    public int? FldZjf { get; set; }

    public string? FldYy { get; set; }

    public int? FldBd { get; set; }

    public int? FldPay { get; set; }

    public int? FldIncome { get; set; }

    public string? FldCardold { get; set; }

    public int? FldMoney { get; set; }

    public int? FldTotalAmount { get; set; }

    public int? FldLock { get; set; }

    public string? FldSafeword { get; set; }

    public string FldSpreaderid { get; set; } = null!;

    public int FldSpreaderLevel { get; set; }

    public DateTime? FldRunmore { get; set; }

    public string? FldMachineid { get; set; }

    public int? FldCoin { get; set; }

    public int FldTransferTimes { get; set; }

    public int? FldUserid { get; set; }

    public int? FldQcvip { get; set; }

    public DateTime? FldQcviptim { get; set; }

    public int? FldWcvip { get; set; }

    public DateTime? FldWcviptim { get; set; }

    public int? FldQdcs { get; set; }

    public DateTime? FldQdsj { get; set; }

    public bool? FldChecklogin { get; set; }

    public string? FldCheckip { get; set; }

    public string? FldLanip { get; set; }

    public string? FldPasskey { get; set; }

    public string? FldRefreshKey { get; set; }

    public string? FldTempPasskey { get; set; }

    public DateTime? FldRefreshKeyTimestamp { get; set; }

    public string? FldPasskeyTimestamp { get; set; }
}
