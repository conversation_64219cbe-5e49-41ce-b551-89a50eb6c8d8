using System;
using Akka.Actor;
using HeroLogin.Services;

namespace HeroLogin.Core.Actors
{
    /// <summary>
    /// Quản lý ActorSystem cho toàn bộ ứng dụng
    /// </summary>
    public class ActorSystemManager
    {
        private static ActorSystemManager? _instance;
        private ActorSystem? _actorSystem;
        private IActorRef? _tcpManagerActor;
        private IActorRef? _packetHandlerActor;

        public static ActorSystemManager Instance => _instance ??= new ActorSystemManager();

        public ActorSystem ActorSystem => _actorSystem ?? throw new InvalidOperationException("ActorSystem chưa được khởi tạo");

        public IActorRef TcpManagerActor => _tcpManagerActor ?? throw new InvalidOperationException("TcpManagerActor chưa được khởi tạo");

        public IActorRef PacketHandlerActor => _packetHandlerActor ?? throw new InvalidOperationException("PacketHandlerActor chưa được khởi tạo");

        private ActorSystemManager()
        {
            // Private constructor for singleton pattern
        }

        /// <summary>
        /// Khởi tạo ActorSystem và các actor chính
        /// </summary>
        public void Initialize()
        {
            try
            {
                // Tạo ActorSystem
                _actorSystem = ActorSystem.Create("HeroLoginSystem");

                // Tạo PacketHandlerActor trước vì TcpManagerActor sẽ cần tham chiếu đến nó
                _packetHandlerActor = _actorSystem.ActorOf(Props.Create(() => new PacketHandlerActor()), "packetHandler");

                // Tạo TcpManagerActor
                _tcpManagerActor = _actorSystem.ActorOf(Props.Create(() => new TcpManagerActor(_packetHandlerActor)), "tcpManager");

                // Gửi tham chiếu TcpManagerActor đến PacketHandlerActor
                _packetHandlerActor.Tell(new SetTcpManagerActor(_tcpManagerActor));

                Logger.Instance.Info("ActorSystem đã được khởi tạo thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi tạo ActorSystem: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Dừng ActorSystem
        /// </summary>
        public void Shutdown()
        {
            try
            {
                if (_actorSystem != null)
                {
                    _actorSystem.Terminate().Wait();
                    _actorSystem = null;
                    _tcpManagerActor = null;
                    _packetHandlerActor = null;

                    Logger.Instance.Info("ActorSystem đã được dừng thành công");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng ActorSystem: {ex.Message}");
            }
        }
    }
}
