using System;
using Akka.Actor;
using Akka.IO;
using HeroLogin.Services;

namespace HeroLogin.Core.Actors
{
    /// <summary>
    /// Actor x<PERSON> lý kết nối của một client cụ thể
    /// </summary>
    public class ClientActor : ReceiveActor
    {
        private readonly IActorRef _connection;
        private readonly ClientSession _session;
        private readonly IActorRef _packetHandlerActor;

        public ClientActor(IActorRef connection, ClientSession session, IActorRef packetHandlerActor)
        {
            _connection = connection;
            _session = session;
            _packetHandlerActor = packetHandlerActor;

            // Đ<PERSON>nh nghĩa các message handler
            Receive<Tcp.ConnectionClosed>(msg => HandleConnectionClosed(msg));
        }

        private void HandleConnectionClosed(Tcp.ConnectionClosed message)
        {
            // K<PERSON>t nối đã đóng, dừng actor
            Context.Stop(Self);
        }
    }

    /// <summary>
    /// Message yêu cầu xử lý gói tin
    /// </summary>
    public class ProcessPacket
    {
        public IActorRef Connection { get; }
        public ClientSession Session { get; }
        public byte[] Data { get; }

        public ProcessPacket(IActorRef connection, ClientSession session, byte[] data)
        {
            Connection = connection;
            Session = session;
            Data = data;
        }
    }
}
