using System;
using System.Net;
using Akka.Actor;

namespace HeroLogin.Core.Actors
{
    /// <summary>
    /// <PERSON><PERSON>u trữ thông tin về phiên kết n<PERSON>i của client
    /// </summary>
    public class ClientSession
    {
        public int SessionId { get; }
        public IPEndPoint RemoteEndPoint { get; }
        public DateTime ConnectedTime { get; }
        public DateTime LastActivityTime { get; private set; }
        public bool IsAuthenticated { get; set; }
        public string? AccountId { get; set; }
        public IActorRef Connection { get; }
        public string? SelectedServerId { get; set; }

        public ClientSession(int sessionId, EndPoint remoteEndPoint, IActorRef connection)
        {
            SessionId = sessionId;
            RemoteEndPoint = (IPEndPoint)remoteEndPoint;
            ConnectedTime = DateTime.Now;
            LastActivityTime = DateTime.Now;
            IsAuthenticated = false;
            Connection = connection;
        }

        public void UpdateActivity()
        {
            LastActivityTime = DateTime.Now;
        }
    }
}
