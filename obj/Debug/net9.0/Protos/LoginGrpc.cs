// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Protos/login.proto
// </auto-generated>
#pragma warning disable 0414, 1591, 8981, 0612
#region Designer generated code

using grpc = global::Grpc.Core;

namespace HeroLogin.Protos {
  /// <summary>
  /// D<PERSON><PERSON> vụ xác thực tài kho<PERSON>n
  /// </summary>
  public static partial class LoginAuth
  {
    static readonly string __ServiceName = "login.LoginAuth";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HeroLogin.Protos.VerifyTokenRequest> __Marshaller_login_VerifyTokenRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HeroLogin.Protos.VerifyTokenRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HeroLogin.Protos.VerifyTokenResponse> __Marshaller_login_VerifyTokenResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HeroLogin.Protos.VerifyTokenResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HeroLogin.Protos.RegisterGameServerRequest> __Marshaller_login_RegisterGameServerRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HeroLogin.Protos.RegisterGameServerRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HeroLogin.Protos.RegisterGameServerResponse> __Marshaller_login_RegisterGameServerResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HeroLogin.Protos.RegisterGameServerResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HeroLogin.Protos.UpdateGameServerStatusRequest> __Marshaller_login_UpdateGameServerStatusRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HeroLogin.Protos.UpdateGameServerStatusRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HeroLogin.Protos.UpdateGameServerStatusResponse> __Marshaller_login_UpdateGameServerStatusResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HeroLogin.Protos.UpdateGameServerStatusResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HeroLogin.Protos.TransmitMessageRequest> __Marshaller_login_TransmitMessageRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HeroLogin.Protos.TransmitMessageRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HeroLogin.Protos.TransmitMessageResponse> __Marshaller_login_TransmitMessageResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HeroLogin.Protos.TransmitMessageResponse.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HeroLogin.Protos.VerifyTokenRequest, global::HeroLogin.Protos.VerifyTokenResponse> __Method_VerifyToken = new grpc::Method<global::HeroLogin.Protos.VerifyTokenRequest, global::HeroLogin.Protos.VerifyTokenResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "VerifyToken",
        __Marshaller_login_VerifyTokenRequest,
        __Marshaller_login_VerifyTokenResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HeroLogin.Protos.RegisterGameServerRequest, global::HeroLogin.Protos.RegisterGameServerResponse> __Method_RegisterGameServer = new grpc::Method<global::HeroLogin.Protos.RegisterGameServerRequest, global::HeroLogin.Protos.RegisterGameServerResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "RegisterGameServer",
        __Marshaller_login_RegisterGameServerRequest,
        __Marshaller_login_RegisterGameServerResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HeroLogin.Protos.UpdateGameServerStatusRequest, global::HeroLogin.Protos.UpdateGameServerStatusResponse> __Method_UpdateGameServerStatus = new grpc::Method<global::HeroLogin.Protos.UpdateGameServerStatusRequest, global::HeroLogin.Protos.UpdateGameServerStatusResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "UpdateGameServerStatus",
        __Marshaller_login_UpdateGameServerStatusRequest,
        __Marshaller_login_UpdateGameServerStatusResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HeroLogin.Protos.TransmitMessageRequest, global::HeroLogin.Protos.TransmitMessageResponse> __Method_TransmitMessage = new grpc::Method<global::HeroLogin.Protos.TransmitMessageRequest, global::HeroLogin.Protos.TransmitMessageResponse>(
        grpc::MethodType.DuplexStreaming,
        __ServiceName,
        "TransmitMessage",
        __Marshaller_login_TransmitMessageRequest,
        __Marshaller_login_TransmitMessageResponse);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::HeroLogin.Protos.LoginReflection.Descriptor.Services[0]; }
    }

    /// <summary>Base class for server-side implementations of LoginAuth</summary>
    [grpc::BindServiceMethod(typeof(LoginAuth), "BindService")]
    public abstract partial class LoginAuthBase
    {
      /// <summary>
      /// Xác thực token đăng nhập
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HeroLogin.Protos.VerifyTokenResponse> VerifyToken(global::HeroLogin.Protos.VerifyTokenRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// Đăng ký GameServer với LoginServer
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HeroLogin.Protos.RegisterGameServerResponse> RegisterGameServer(global::HeroLogin.Protos.RegisterGameServerRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// Cập nhật trạng thái của GameServer
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HeroLogin.Protos.UpdateGameServerStatusResponse> UpdateGameServerStatus(global::HeroLogin.Protos.UpdateGameServerStatusRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task TransmitMessage(grpc::IAsyncStreamReader<global::HeroLogin.Protos.TransmitMessageRequest> requestStream, grpc::IServerStreamWriter<global::HeroLogin.Protos.TransmitMessageResponse> responseStream, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(LoginAuthBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_VerifyToken, serviceImpl.VerifyToken)
          .AddMethod(__Method_RegisterGameServer, serviceImpl.RegisterGameServer)
          .AddMethod(__Method_UpdateGameServerStatus, serviceImpl.UpdateGameServerStatus)
          .AddMethod(__Method_TransmitMessage, serviceImpl.TransmitMessage).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, LoginAuthBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_VerifyToken, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HeroLogin.Protos.VerifyTokenRequest, global::HeroLogin.Protos.VerifyTokenResponse>(serviceImpl.VerifyToken));
      serviceBinder.AddMethod(__Method_RegisterGameServer, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HeroLogin.Protos.RegisterGameServerRequest, global::HeroLogin.Protos.RegisterGameServerResponse>(serviceImpl.RegisterGameServer));
      serviceBinder.AddMethod(__Method_UpdateGameServerStatus, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HeroLogin.Protos.UpdateGameServerStatusRequest, global::HeroLogin.Protos.UpdateGameServerStatusResponse>(serviceImpl.UpdateGameServerStatus));
      serviceBinder.AddMethod(__Method_TransmitMessage, serviceImpl == null ? null : new grpc::DuplexStreamingServerMethod<global::HeroLogin.Protos.TransmitMessageRequest, global::HeroLogin.Protos.TransmitMessageResponse>(serviceImpl.TransmitMessage));
    }

  }
}
#endregion
